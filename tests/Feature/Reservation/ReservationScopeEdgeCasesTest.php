<?php

namespace Tests\Feature\Reservation;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Models\Reservation::class)]
class ReservationScopeEdgeCasesTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->field = Field::factory()->active()->create();
    }

    #[Test]
    public function upcoming_scope_handles_exact_current_time_boundary()
    {
        // Set current time to exactly 12:00:00
        $this->travelTo(now()->setTime(12, 0, 0));

        // Create reservations at exact boundary times
        $exactCurrentTime = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '12:00:00',
            'status' => 'Confirmed',
        ]);

        $oneSecondLater = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '12:00:01',
            'status' => 'Confirmed',
        ]);

        $oneSecondEarlier = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '11:59:59',
            'status' => 'Confirmed',
        ]);

        $upcomingReservations = Reservation::upcoming()->get();

        // Exact current time should NOT be included (must be > current time)
        $this->assertFalse($upcomingReservations->contains($exactCurrentTime));

        // One second later should be included
        $this->assertTrue($upcomingReservations->contains($oneSecondLater));

        // One second earlier should NOT be included
        $this->assertFalse($upcomingReservations->contains($oneSecondEarlier));
    }

    #[Test]
    public function upcoming_scope_handles_midnight_crossover()
    {
        // Set current time to 11:30 PM
        $this->travelTo(now()->setTime(23, 30));

        // Create reservation for tomorrow at 1:00 AM
        $tomorrowEarly = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->addDay(),
            'start_time' => '01:00',
            'status' => 'Confirmed',
        ]);

        // Create reservation for today at 11:45 PM (15 minutes from now)
        $todayLate = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '23:45',
            'status' => 'Confirmed',
        ]);

        // Create reservation for today at 11:15 PM (15 minutes ago)
        $todayPast = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '23:15',
            'status' => 'Confirmed',
        ]);

        $upcomingReservations = Reservation::upcoming()->get();

        $this->assertTrue($upcomingReservations->contains($tomorrowEarly));
        $this->assertTrue($upcomingReservations->contains($todayLate));
        $this->assertFalse($upcomingReservations->contains($todayPast));
    }

    #[Test]
    public function upcoming_scope_handles_different_date_formats()
    {
        // Set current time to 12:00 PM
        $this->travelTo(now()->setTime(12, 0));

        // Create reservations with different time formats
        $futureReservation1 = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '14:30:00', // With seconds
            'status' => 'Confirmed',
        ]);

        $futureReservation2 = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '15:45', // Without seconds
            'status' => 'Confirmed',
        ]);

        $upcomingReservations = Reservation::upcoming()->get();

        $this->assertTrue($upcomingReservations->contains($futureReservation1));
        $this->assertTrue($upcomingReservations->contains($futureReservation2));
    }

    #[Test]
    public function upcoming_scope_ignores_cancelled_reservations()
    {
        // Set current time to 12:00 PM
        $this->travelTo(now()->setTime(12, 0));

        $confirmedFuture = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '14:00',
            'status' => 'Confirmed',
        ]);

        $cancelledFuture = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '15:00',
            'status' => 'Cancelled',
        ]);

        $pendingFuture = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '16:00',
            'status' => 'Pending',
        ]);

        $upcomingReservations = Reservation::upcoming()->get();

        // Upcoming scope should include all statuses (it doesn't filter by status)
        $this->assertTrue($upcomingReservations->contains($confirmedFuture));
        $this->assertTrue($upcomingReservations->contains($cancelledFuture));
        $this->assertTrue($upcomingReservations->contains($pendingFuture));
    }

    #[Test]
    public function active_scope_excludes_cancelled_reservations()
    {
        $confirmedReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed',
        ]);

        $pendingReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Pending',
        ]);

        $cancelledReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Cancelled',
        ]);

        $activeReservations = Reservation::active()->get();

        $this->assertTrue($activeReservations->contains($confirmedReservation));
        $this->assertTrue($activeReservations->contains($pendingReservation));
        $this->assertFalse($activeReservations->contains($cancelledReservation));
    }

    #[Test]
    public function today_scope_only_includes_current_date()
    {
        $todayReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
        ]);

        $yesterdayReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->subDay(),
        ]);

        $tomorrowReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->addDay(),
        ]);

        $todayReservations = Reservation::today()->get();

        $this->assertTrue($todayReservations->contains($todayReservation));
        $this->assertFalse($todayReservations->contains($yesterdayReservation));
        $this->assertFalse($todayReservations->contains($tomorrowReservation));
    }

    #[Test]
    public function scopes_can_be_chained_together()
    {
        // Set current time to 12:00 PM
        $this->travelTo(now()->setTime(12, 0));

        // Create various reservations
        $upcomingActiveReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '14:00',
            'status' => 'Confirmed',
        ]);

        $upcomingCancelledReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '15:00',
            'status' => 'Cancelled',
        ]);

        $pastActiveReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '10:00',
            'status' => 'Confirmed',
        ]);

        // Test chaining upcoming + active scopes
        $upcomingActiveReservations = Reservation::upcoming()->active()->get();

        $this->assertTrue($upcomingActiveReservations->contains($upcomingActiveReservation));
        $this->assertFalse($upcomingActiveReservations->contains($upcomingCancelledReservation));
        $this->assertFalse($upcomingActiveReservations->contains($pastActiveReservation));

        // Test chaining today + active scopes
        $todayActiveReservations = Reservation::today()->active()->get();

        $this->assertTrue($todayActiveReservations->contains($upcomingActiveReservation));
        $this->assertFalse($todayActiveReservations->contains($upcomingCancelledReservation));
        $this->assertTrue($todayActiveReservations->contains($pastActiveReservation));
    }
}
